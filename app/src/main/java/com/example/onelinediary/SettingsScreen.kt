package com.example.onelinediary

import android.widget.Toast
import androidx.compose.foundation.border
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.onelinediary.components.standardButtonColors
import com.example.onelinediary.components.standardButtonModifier
import com.example.onelinediary.components.standardButtonPadding
import com.example.onelinediary.components.standardButtonShape
import com.example.onelinediary.components.backButtonModifier
import com.example.onelinediary.components.getSoftPastelColor
import com.example.onelinediary.components.getSoftComplementaryBackgroundColor
import com.example.onelinediary.ui.theme.*
import com.example.onelinediary.ui.theme.LocalAppTheme
import com.example.onelinediary.ui.theme.AppTheme

// Helper function for theme selection button colors
@Composable
fun standardOutlinedButtonColors(isSelected: Boolean) = ButtonDefaults.outlinedButtonColors(
    containerColor = if (isSelected) {
        when (LocalAppTheme.current) {
            AppTheme.BLACK_WHITE -> Color(0xFF757575) // Neutral gray for black and white theme
            else -> MaterialTheme.colorScheme.primary // Standard theme color
        }
    } else Color.Transparent,
    contentColor = MaterialTheme.colorScheme.onBackground,
    disabledContainerColor = getSoftComplementaryBackgroundColor(), // Soft complementary background for disabled states
    disabledContentColor = getSoftPastelColor() // Use soft pastel color for disabled states
)

// Helper function for theme selection button modifier - borderless like back button
@Composable
fun standardButtonModifier(isSelected: Boolean = false): Modifier {
    val backgroundColor = if (isSelected) {
        when (LocalAppTheme.current) {
            AppTheme.BLACK_WHITE -> Color(0xFF757575) // Neutral gray for black and white theme
            else -> MaterialTheme.colorScheme.primary // Standard theme color
        }
    } else Color.Transparent

    return Modifier
        .height(48.dp)
        .background(
            color = backgroundColor,
            shape = RoundedCornerShape(8.dp)
        )
}

@Composable
fun SettingsScreen(
    onBack: () -> Unit,
    onDownload: () -> Unit,
    onCalendar: () -> Unit,
    saveDateManager: SaveDateManager
) {
    val context = LocalContext.current
    // Get the background color from the theme
    val backgroundColor = MaterialTheme.colorScheme.background

    // Get the current theme
    val currentTheme = LocalAppTheme.current

    // Create a ThemeManager instance
    val themeManager = remember { ThemeManager.getInstance(context) }

    // State to track the selected theme - make it reactive to theme changes
    var selectedTheme by remember { mutableStateOf(themeManager.currentTheme.value) }

    // Update selectedTheme when the actual theme changes
    LaunchedEffect(currentTheme) {
        selectedTheme = currentTheme
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .padding(16.dp),
        verticalArrangement = Arrangement.Top
    ) {
        // Top navigation row with back button
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Back button
            Button(
                onClick = onBack,
                modifier = backButtonModifier(),
                contentPadding = standardButtonPadding,
                colors = standardButtonColors(),
                shape = standardButtonShape
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "Back",
                    tint = MaterialTheme.colorScheme.onBackground
                )
            }

            // Title
            Text(
                text = "Settings",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )

            // Empty spacer for alignment
            Spacer(modifier = Modifier.width(64.dp))
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Settings options
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // Download option
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 12.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Download All Content",
                        style = MaterialTheme.typography.bodyLarge
                    )

                    Button(
                        onClick = onDownload,
                        modifier = standardButtonModifier(),
                        contentPadding = PaddingValues(8.dp),
                        colors = standardButtonColors(),
                        shape = standardButtonShape
                    ) {
                        Icon(
                            painter = androidx.compose.ui.res.painterResource(id = R.drawable.ic_download),
                            contentDescription = "Download",
                            tint = MaterialTheme.colorScheme.onBackground
                        )
                    }
                }

                HorizontalDivider()

                // Calendar option
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 12.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Calendar",
                        style = MaterialTheme.typography.bodyLarge
                    )

                    Button(
                        onClick = onCalendar,
                        modifier = standardButtonModifier(),
                        contentPadding = PaddingValues(8.dp),
                        colors = standardButtonColors(),
                        shape = standardButtonShape
                    ) {
                        Icon(
                            painter = androidx.compose.ui.res.painterResource(id = R.drawable.ic_calendar),
                            contentDescription = "Calendar",
                            tint = MaterialTheme.colorScheme.onBackground
                        )
                    }
                }

                HorizontalDivider()

                // Reset Today's Data option
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 12.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Reset Today's Data",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onBackground
                    )

                    var showTodayConfirmDialog by remember { mutableStateOf(false) }

                    Button(
                        onClick = { showTodayConfirmDialog = true },
                        modifier = standardButtonModifier(),
                        contentPadding = PaddingValues(8.dp),
                        colors = standardButtonColors(),
                        shape = standardButtonShape
                    ) {
                        Icon(
                            painter = androidx.compose.ui.res.painterResource(id = R.drawable.ic_reset),
                            contentDescription = "Reset Today",
                            tint = MaterialTheme.colorScheme.onBackground
                        )
                    }

                    if (showTodayConfirmDialog) {
                        AlertDialog(
                            onDismissRequest = { showTodayConfirmDialog = false },
                            title = { Text("Reset Today's Data?") },
                            text = { Text("This will clear only today's entries. Past entries will be preserved. This action cannot be undone.") },
                            confirmButton = {
                                Button(
                                    onClick = {
                                        // Use the clearTodayData method
                                        val success = saveDateManager.clearTodayData()
                                        showTodayConfirmDialog = false

                                        // Show a toast with the result
                                        val message = if (success) {
                                            "Today's data has been reset"
                                        } else {
                                            "Reset partially completed. Some of today's files may remain."
                                        }
                                        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                                    },
                                    modifier = standardButtonModifier(),
                                    contentPadding = standardButtonPadding,
                                    colors = standardButtonColors(),
                                    shape = standardButtonShape
                                ) {
                                    Text("Reset", color = MaterialTheme.colorScheme.onBackground)
                                }
                            },
                            dismissButton = {
                                Button(
                                    onClick = { showTodayConfirmDialog = false },
                                    modifier = standardButtonModifier(),
                                    contentPadding = standardButtonPadding,
                                    colors = standardButtonColors(),
                                    shape = standardButtonShape
                                ) {
                                    Text("Cancel", color = MaterialTheme.colorScheme.onBackground)
                                }
                            }
                        )
                    }
                }

                HorizontalDivider()

                // Color Selection option
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 12.dp)
                ) {
                    Text(
                        text = "Colour",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onBackground,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    // Color grid - 2 rows of 4 colors each
                    val colorThemes = listOf(
                        AppTheme.STANDARD to BackgroundGreen,
                        AppTheme.BLUE to BackgroundBlue,
                        AppTheme.PINK to BackgroundPink,
                        AppTheme.PURPLE to BackgroundPurple,
                        AppTheme.YELLOW to BackgroundYellow,
                        AppTheme.ORANGE to BackgroundOrange,
                        AppTheme.RED to BackgroundRed,
                        AppTheme.TEAL to BackgroundTeal,
                        AppTheme.LAVENDER to BackgroundLavender,
                        AppTheme.BLACK_WHITE to BackgroundWhite
                    )

                    // First row of colors
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        colorThemes.take(5).forEach { (theme, color) ->
                            ColorSelectionButton(
                                color = color,
                                isSelected = selectedTheme == theme,
                                onClick = {
                                    selectedTheme = theme
                                    themeManager.setTheme(theme)
                                    val themeName = when (theme) {
                                        AppTheme.STANDARD -> "Standard"
                                        AppTheme.BLACK_WHITE -> "Black & White"
                                        AppTheme.BLUE -> "Blue"
                                        AppTheme.PINK -> "Pink"
                                        AppTheme.PURPLE -> "Purple"
                                        AppTheme.YELLOW -> "Yellow"
                                        AppTheme.ORANGE -> "Orange"
                                        AppTheme.RED -> "Red"
                                        AppTheme.TEAL -> "Teal"
                                        AppTheme.LAVENDER -> "Lavender"
                                    }
                                    Toast.makeText(context, "$themeName theme applied", Toast.LENGTH_SHORT).show()
                                },
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // Second row of colors
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        colorThemes.drop(5).forEach { (theme, color) ->
                            ColorSelectionButton(
                                color = color,
                                isSelected = selectedTheme == theme,
                                onClick = {
                                    selectedTheme = theme
                                    themeManager.setTheme(theme)
                                    val themeName = when (theme) {
                                        AppTheme.STANDARD -> "Standard"
                                        AppTheme.BLACK_WHITE -> "Black & White"
                                        AppTheme.BLUE -> "Blue"
                                        AppTheme.PINK -> "Pink"
                                        AppTheme.PURPLE -> "Purple"
                                        AppTheme.YELLOW -> "Yellow"
                                        AppTheme.ORANGE -> "Orange"
                                        AppTheme.RED -> "Red"
                                        AppTheme.TEAL -> "Teal"
                                        AppTheme.LAVENDER -> "Lavender"
                                    }
                                    Toast.makeText(context, "$themeName theme applied", Toast.LENGTH_SHORT).show()
                                },
                                modifier = Modifier.weight(1f)
                            )
                        }
                        // Add empty spaces to fill the row if needed
                        repeat(5 - colorThemes.drop(5).size) {
                            Spacer(modifier = Modifier.weight(1f))
                        }
                    }

                    // Color description
                    Text(
                        text = "Choose your preferred background color",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f),
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }

                HorizontalDivider()

                // Language Selection option
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 12.dp)
                ) {
                    Text(
                        text = "Language",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onBackground,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    // Language options
                    var selectedLanguage by remember { mutableStateOf("English") }

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        // English option
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .weight(1f)
                                .clickable { selectedLanguage = "English" }
                        ) {
                            RadioButton(
                                selected = selectedLanguage == "English",
                                onClick = { selectedLanguage = "English" },
                                colors = RadioButtonDefaults.colors(
                                    selectedColor = MaterialTheme.colorScheme.primary,
                                    unselectedColor = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            )
                            Text(
                                text = "English - Engels",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onBackground,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }

                        // Dutch option
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .weight(1f)
                                .clickable { selectedLanguage = "Dutch" }
                        ) {
                            RadioButton(
                                selected = selectedLanguage == "Dutch",
                                onClick = { selectedLanguage = "Dutch" },
                                colors = RadioButtonDefaults.colors(
                                    selectedColor = MaterialTheme.colorScheme.primary,
                                    unselectedColor = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            )
                            Text(
                                text = "Dutch - Nederlands",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onBackground,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }
                    }
                }

                HorizontalDivider()

                // Reset All Data option
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 12.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Reset All Data (Test Only)",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.error
                    )

                    var showConfirmDialog by remember { mutableStateOf(false) }

                    Button(
                        onClick = { showConfirmDialog = true },
                        modifier = standardButtonModifier(),
                        contentPadding = PaddingValues(8.dp),
                        colors = standardButtonColors(),
                        shape = standardButtonShape
                    ) {
                        Icon(
                            painter = androidx.compose.ui.res.painterResource(id = R.drawable.ic_reset),
                            contentDescription = "Reset All",
                            tint = MaterialTheme.colorScheme.onBackground
                        )
                    }

                    if (showConfirmDialog) {
                        AlertDialog(
                            onDismissRequest = { showConfirmDialog = false },
                            title = { Text("Reset All Data?") },
                            text = { Text("This will clear all saved entries. This action cannot be undone.") },
                            confirmButton = {
                                Button(
                                    onClick = {
                                        // Use the more comprehensive clearAllData method
                                        val success = saveDateManager.clearAllData()
                                        showConfirmDialog = false

                                        // Show a toast with the result
                                        val message = if (success) {
                                            "All data has been reset"
                                        } else {
                                            "Reset partially completed. Some files may remain."
                                        }
                                        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                                    },
                                    modifier = standardButtonModifier(),
                                    contentPadding = standardButtonPadding,
                                    colors = standardButtonColors(),
                                    shape = standardButtonShape
                                ) {
                                    Text("Reset", color = MaterialTheme.colorScheme.error)
                                }
                            },
                            dismissButton = {
                                Button(
                                    onClick = { showConfirmDialog = false },
                                    modifier = standardButtonModifier(),
                                    contentPadding = standardButtonPadding,
                                    colors = standardButtonColors(),
                                    shape = standardButtonShape
                                ) {
                                    Text("Cancel", color = MaterialTheme.colorScheme.onBackground)
                                }
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun ColorSelectionButton(
    color: Color,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .aspectRatio(1f)
            .clickable { onClick() }
            .background(
                color = color,
                shape = CircleShape
            ),
        contentAlignment = Alignment.Center
    ) {
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "Selected",
                tint = MaterialTheme.colorScheme.onBackground,
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

